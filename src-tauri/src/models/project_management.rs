use chrono::{DateTime, Utc};
use serde::{Deserialize, Serialize};

/// 项目模型
#[derive(Debug, Serialize, Deserialize)]
pub struct Project {
    pub project_id: Option<String>,
    pub project_name: String,
    pub project_short_name: String,
    pub project_path: Option<String>,
    pub disease_item_id: Option<i64>,
    pub project_stage_item_id: Option<i64>,
    pub project_status_item_id: Option<i64>,
    pub recruitment_status_item_id: Option<i64>,
    pub contract_case_total: Option<i64>,
    pub project_start_date: Option<String>,
    pub last_updated: Option<String>,
    pub drug_mechanism: Option<String>,
    pub drug_mechanism_item_id: Option<i64>,  // 药物作用机制字典ID
    pub drug_introduction: Option<String>,
}

/// 项目申办方模型
#[derive(Debug, Serialize, Deserialize)]
pub struct ProjectSponsor {
    pub id: Option<i64>,
    pub project_id: String,
    pub sponsor_item_id: i64,
}

/// 研究药物模型
#[derive(Debug, Serialize, Deserialize)]
pub struct ResearchDrug {
    pub drug_info_id: Option<i64>,
    pub project_id: String,
    pub research_drug: String,
    // 药物类型和基本信息
    pub drug_type_item_id: Option<i64>,            // 药物类型：研究药物/对照药物/安慰剂
    pub drug_classification_item_id: Option<i64>,  // 药物分类（关联字典）
    pub usage_method_item_id: Option<i64>,         // 用药方法（关联字典）
    pub usage_frequency_item_id: Option<i64>,      // 用药频率（关联字典）
    pub mechanism_of_action_item_id: Option<i64>,  // 药物作用机制（关联字典）
    pub dosage: Option<String>,                    // 剂量信息
    pub share: Option<f64>,                        // 份额/占比
    pub drug_characteristics: Option<String>,      // 药物特性描述
    pub notes: Option<String>,                     // 其他备注信息
}

/// 药物分组模型
#[derive(Debug, Serialize, Deserialize)]
pub struct DrugGroup {
    pub group_id: Option<i64>,
    pub project_id: String,
    pub drug_name: String,
    pub share: f64,
}

/// 项目人员角色模型
#[derive(Debug, Serialize, Deserialize)]
pub struct ProjectPersonnelRole {
    pub assignment_id: Option<i64>,
    pub project_id: String,
    pub personnel_id: i64,
    pub role_item_id: i64,
}

/// 补贴方案模型
#[derive(Debug, Serialize, Deserialize)]
pub struct SubsidyScheme {
    pub scheme_id: Option<i64>,
    pub project_id: String,
    pub scheme_name: String,
    pub total_amount: f64,
    pub included_subsidies: Option<Vec<i64>>,
}

/// 补贴项模型
#[derive(Debug, Serialize, Deserialize)]
pub struct Subsidy {
    pub subsidy_item_id: Option<i64>,
    pub project_id: String,
    pub subsidy_type_item_id: i64,
    pub unit_amount: f64,
    pub total_units: i64,
    pub unit_item_id: i64,
    pub total_amount: f64,
    pub last_updated: Option<String>,
}

/// 项目查询参数
#[derive(Debug, Serialize, Deserialize)]
pub struct ProjectQuery {
    pub name: Option<String>,
    pub disease_item_id: Option<i64>,
    pub project_stage_item_id: Option<i64>,
    pub project_status_item_id: Option<i64>,
    pub recruitment_status_item_id: Option<i64>,
    // 新增：按申办方筛选（字典项ID列表）
    #[serde(skip_serializing_if = "Option::is_none")]
    pub sponsor_item_ids: Option<Vec<i64>>,
    // 新增：按PI人员筛选（人员ID列表，限定角色为“主要研究者”）
    #[serde(skip_serializing_if = "Option::is_none")]
    pub pi_personnel_ids: Option<Vec<i64>>,
    pub page: Option<i64>,
    pub page_size: Option<i64>,
    pub sort_by: Option<String>,
    pub sort_order: Option<String>,
}

/// 项目分页结果
#[derive(Debug, Serialize, Deserialize)]
pub struct ProjectPagination {
    pub items: Vec<ProjectWithDetails>,
    pub total: i64,
    pub page: i64,
    pub page_size: i64,
}

/// 带详情的研究药物模型
#[derive(Debug, Serialize, Deserialize)]
pub struct ResearchDrugWithDetails {
    pub drug_info_id: Option<i64>,
    pub project_id: String,
    pub research_drug: String,
    // 药物类型和基本信息
    pub drug_type_item_id: Option<i64>,
    pub drug_classification_item_id: Option<i64>,
    pub usage_method_item_id: Option<i64>,
    pub usage_frequency_item_id: Option<i64>,
    pub mechanism_of_action_item_id: Option<i64>,
    pub dosage: Option<String>,
    pub share: Option<f64>,
    pub drug_characteristics: Option<String>,
    pub notes: Option<String>,
    // 关联的字典项详情
    pub drug_type: Option<DictionaryItem>,            // 药物类型详情
    pub drug_classification: Option<DictionaryItem>,  // 药物分类详情
    pub usage_method: Option<DictionaryItem>,         // 用药方法详情
    pub usage_frequency: Option<DictionaryItem>,      // 用药频率详情
    pub mechanism_of_action: Option<DictionaryItem>,  // 药物作用机制详情
}

/// 入组/排除标准统计信息
#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct CriteriaStats {
    pub inclusion_count: i64,    // 入组标准数量
    pub exclusion_count: i64,    // 排除标准数量
    pub total_count: i64,        // 总标准数量
    pub has_criteria: bool,      // 是否配置了标准
}

impl Default for CriteriaStats {
    fn default() -> Self {
        Self {
            inclusion_count: 0,
            exclusion_count: 0,
            total_count: 0,
            has_criteria: false,
        }
    }
}

/// 项目详情（包含关联数据）
#[derive(Debug, Serialize, Deserialize)]
pub struct ProjectWithDetails {
    pub project: Project,
    pub disease: Option<DictionaryItem>,
    pub project_stage: Option<DictionaryItem>,
    pub project_status: Option<DictionaryItem>,
    pub recruitment_status: Option<DictionaryItem>,
    pub sponsors: Option<Vec<ProjectSponsorWithDetails>>,
    pub research_drugs: Option<Vec<ResearchDrugWithDetails>>,
    pub drug_groups: Option<Vec<DrugGroup>>,
    pub personnel: Option<Vec<ProjectPersonnelWithDetails>>,
    pub subsidy_schemes: Option<Vec<SubsidyScheme>>,
    pub subsidies: Option<Vec<SubsidyWithDetails>>,
    pub criteria_stats: Option<CriteriaStats>,  // 入组/排除标准统计信息
    pub has_recruitment_companies: Option<bool>,  // 是否有招募公司政策
}

/// 带详情的项目申办方
#[derive(Debug, Serialize, Deserialize)]
pub struct ProjectSponsorWithDetails {
    pub id: Option<i64>,
    pub project_id: String,
    pub sponsor_item_id: i64,
    pub sponsor: Option<DictionaryItem>,
}

/// 带详情的项目人员
#[derive(Debug, Serialize, Deserialize)]
pub struct ProjectPersonnelWithDetails {
    pub assignment_id: Option<i64>,
    pub project_id: String,
    pub personnel_id: i64,
    pub role_item_id: i64,
    pub personnel: Option<Personnel>,
    pub role: Option<DictionaryItem>,
}

/// 带详情的补贴项
#[derive(Debug, Serialize, Deserialize)]
pub struct SubsidyWithDetails {
    pub subsidy_item_id: Option<i64>,
    pub project_id: String,
    pub subsidy_type_item_id: i64,
    pub unit_amount: f64,
    pub total_units: i64,
    pub unit_item_id: i64,
    pub total_amount: f64,
    pub last_updated: Option<String>,
    pub subsidy_type: Option<DictionaryItem>,
    pub unit: Option<DictionaryItem>,
}

/// 字典项
#[derive(Debug, Serialize, Deserialize)]
pub struct DictionaryItem {
    pub item_id: i64,
    pub dictionary_id: i64,
    pub item_key: String,
    pub item_value: String,
    pub item_description: Option<String>,
    pub status: Option<String>,
}

/// 人员信息
#[derive(Debug, Serialize, Deserialize)]
pub struct Personnel {
    pub id: i64,
    pub name: String,
    pub gender: Option<String>,
    pub birthday: Option<String>,
    pub phone: Option<String>,
    pub email: Option<String>,
    pub position_item_id: Option<i64>,
    pub isPI: Option<bool>,
    pub organization: Option<String>,
}

/// 创建项目请求
#[derive(Debug, Serialize, Deserialize)]
pub struct CreateProjectRequest {
    pub project: Project,
    pub sponsors: Option<Vec<ProjectSponsor>>,
    pub research_drugs: Option<Vec<ResearchDrug>>,
    pub drug_groups: Option<Vec<DrugGroup>>,
    pub personnel: Option<Vec<ProjectPersonnelRole>>,
    pub subsidy_schemes: Option<Vec<SubsidyScheme>>,
    pub subsidies: Option<Vec<Subsidy>>,
}

/// CSV导入相关数据结构

/// CSV行数据结构
#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct CsvPersonnelRow {
    /// 项目简称或全称
    pub project_name: String,
    /// 授权人员字符串 (格式: "姓名1(角色1),姓名2(角色2)")
    pub authorized_personnel: String,
    /// 原始行号 (用于错误报告)
    pub row_number: usize,
}

/// 解析后的人员角色信息
#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct ParsedPersonnelRole {
    /// 人员姓名
    pub name: String,
    /// 角色名称
    pub role_name: String,
    /// 原始字符串 (用于错误报告)
    pub original_string: String,
}

/// 项目匹配结果
#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct ProjectMatch {
    /// 匹配到的项目ID
    pub project_id: String,
    /// 项目简称
    pub project_short_name: String,
    /// 项目全称
    pub project_name: String,
    /// 匹配类型 (short_name, full_name)
    pub match_type: String,
}

/// 人员匹配结果
#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct PersonnelMatch {
    /// 匹配到的人员ID
    pub personnel_id: i64,
    /// 人员姓名
    pub name: String,
    /// 是否精确匹配
    pub exact_match: bool,
}

/// 角色匹配结果
#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct RoleMatch {
    /// 匹配到的角色ID
    pub role_item_id: i64,
    /// 角色名称
    pub role_name: String,
    /// 是否精确匹配
    pub exact_match: bool,
}

/// CSV导入验证结果
#[derive(Debug, Serialize, Deserialize)]
pub struct CsvImportValidation {
    /// 成功解析的记录
    pub valid_records: Vec<CsvImportRecord>,
    /// 验证错误
    pub errors: Vec<CsvImportError>,
    /// 警告信息
    pub warnings: Vec<CsvImportWarning>,
    /// 统计信息
    pub statistics: CsvImportStatistics,
}

/// CSV导入记录
#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct CsvImportRecord {
    /// 原始行号
    pub row_number: usize,
    /// 项目匹配结果
    pub project_match: ProjectMatch,
    /// 人员角色分配
    pub personnel_assignments: Vec<PersonnelRoleAssignment>,
    /// 原始CSV行数据
    pub original_row: CsvPersonnelRow,
}

/// 人员角色分配
#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct PersonnelRoleAssignment {
    /// 人员匹配结果
    pub personnel_match: PersonnelMatch,
    /// 角色匹配结果
    pub role_match: RoleMatch,
    /// 解析后的人员角色信息
    pub parsed_role: ParsedPersonnelRole,
    /// 是否已存在相同分配
    pub already_exists: bool,
}

/// CSV导入错误
#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct CsvImportError {
    /// 错误类型
    pub error_type: CsvImportErrorType,
    /// 行号
    pub row_number: usize,
    /// 错误消息
    pub message: String,
    /// 相关数据
    pub context: Option<String>,
}

/// CSV导入错误类型
#[derive(Debug, Serialize, Deserialize, Clone)]
pub enum CsvImportErrorType {
    /// 项目未找到
    ProjectNotFound,
    /// 人员未找到
    PersonnelNotFound,
    /// 角色未找到
    RoleNotFound,
    /// 授权人员字符串解析失败
    PersonnelParsingError,
    /// 数据格式错误
    DataFormatError,
    /// 数据库错误
    DatabaseError,
}

/// CSV导入警告
#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct CsvImportWarning {
    /// 警告类型
    pub warning_type: CsvImportWarningType,
    /// 行号
    pub row_number: usize,
    /// 警告消息
    pub message: String,
    /// 相关数据
    pub context: Option<String>,
}

/// CSV导入警告类型
#[derive(Debug, Serialize, Deserialize, Clone)]
pub enum CsvImportWarningType {
    /// 模糊匹配
    FuzzyMatch,
    /// 重复分配
    DuplicateAssignment,
    /// 关键角色缺失
    MissingCriticalRole,
}

/// CSV导入统计信息
#[derive(Debug, Serialize, Deserialize)]
pub struct CsvImportStatistics {
    /// 总行数
    pub total_rows: usize,
    /// 成功处理的行数
    pub successful_rows: usize,
    /// 错误行数
    pub error_rows: usize,
    /// 警告行数
    pub warning_rows: usize,
    /// 总人员分配数
    pub total_assignments: usize,
    /// 新增分配数
    pub new_assignments: usize,
    /// 重复分配数
    pub duplicate_assignments: usize,
    /// 涉及的项目数
    pub affected_projects: usize,
}

/// CSV导入结果
#[derive(Debug, Serialize, Deserialize)]
pub struct CsvImportResult {
    /// 是否成功
    pub success: bool,
    /// 导入的记录数
    pub imported_records: usize,
    /// 跳过的记录数
    pub skipped_records: usize,
    /// 错误信息
    pub errors: Vec<String>,
    /// 警告信息
    pub warnings: Vec<String>,
    /// 详细统计
    pub statistics: CsvImportStatistics,
}

/// 质量控制检查结果
#[derive(Debug, Serialize, Deserialize)]
pub struct QualityControlResult {
    /// 项目ID
    pub project_id: String,
    /// 项目名称
    pub project_name: String,
    /// 缺失的关键角色
    pub missing_critical_roles: Vec<String>,
    /// 是否通过质量检查
    pub quality_passed: bool,
    /// 建议信息
    pub recommendations: Vec<String>,
}

/// 更新项目请求
#[derive(Debug, Serialize, Deserialize)]
pub struct UpdateProjectRequest {
    pub project: Project,
    pub sponsors: Option<Vec<ProjectSponsor>>,
    pub research_drugs: Option<Vec<ResearchDrug>>,
    pub drug_groups: Option<Vec<DrugGroup>>,
    pub personnel: Option<Vec<ProjectPersonnelRole>>,
    pub subsidy_schemes: Option<Vec<SubsidyScheme>>,
    pub subsidies: Option<Vec<Subsidy>>,
}
