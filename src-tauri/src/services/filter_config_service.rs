use rusqlite::Connection;
use crate::models::filter_config::{FilterConfig, CreateFilterConfigRequest, UpdateFilterConfigRequest};
use crate::repositories::filter_config_repository::FilterConfigRepository;

pub struct FilterConfigService;

impl FilterConfigService {
    pub fn init_tables(conn: &Connection) -> Result<(), String> {
        FilterConfigRepository::init_tables(conn)
            .map_err(|e| format!("初始化筛选配置表失败: {}", e))
    }

    pub fn get_all_configs(conn: &Connection) -> Result<Vec<FilterConfig>, String> {
        FilterConfigRepository::get_all(conn)
            .map_err(|e| format!("获取筛选配置列表失败: {}", e))
    }

    pub fn get_config_by_id(conn: &Connection, id: &str) -> Result<Option<FilterConfig>, String> {
        if id.trim().is_empty() {
            return Err("配置ID不能为空".to_string());
        }

        FilterConfigRepository::get_by_id(conn, id)
            .map_err(|e| format!("获取筛选配置失败: {}", e))
    }

    pub fn create_config(conn: &Connection, request: CreateFilterConfigRequest) -> Result<FilterConfig, String> {
        // 验证输入
        if request.name.trim().is_empty() {
            return Err("配置名称不能为空".to_string());
        }

        if request.conditions.is_empty() {
            return Err("至少需要一个筛选条件".to_string());
        }

        // 验证条件
        for condition in &request.conditions {
            if condition.field.trim().is_empty() {
                return Err("筛选字段不能为空".to_string());
            }
            if condition.operator.trim().is_empty() {
                return Err("筛选操作符不能为空".to_string());
            }
        }

        FilterConfigRepository::create(conn, request)
            .map_err(|e| format!("创建筛选配置失败: {}", e))
    }

    pub fn update_config(conn: &Connection, id: &str, request: UpdateFilterConfigRequest) -> Result<FilterConfig, String> {
        if id.trim().is_empty() {
            return Err("配置ID不能为空".to_string());
        }

        // 如果提供了名称，验证不为空
        if let Some(ref name) = request.name {
            if name.trim().is_empty() {
                return Err("配置名称不能为空".to_string());
            }
        }

        // 如果提供了条件，验证不为空
        if let Some(ref conditions) = request.conditions {
            if conditions.is_empty() {
                return Err("至少需要一个筛选条件".to_string());
            }

            for condition in conditions {
                if condition.field.trim().is_empty() {
                    return Err("筛选字段不能为空".to_string());
                }
                if condition.operator.trim().is_empty() {
                    return Err("筛选操作符不能为空".to_string());
                }
            }
        }

        FilterConfigRepository::update(conn, id, request)
            .map_err(|e| format!("更新筛选配置失败: {}", e))
    }

    pub fn delete_config(conn: &Connection, id: &str) -> Result<(), String> {
        if id.trim().is_empty() {
            return Err("配置ID不能为空".to_string());
        }

        // 检查配置是否存在
        match FilterConfigRepository::get_by_id(conn, id) {
            Ok(Some(_)) => {
                FilterConfigRepository::delete(conn, id)
                    .map_err(|e| format!("删除筛选配置失败: {}", e))
            }
            Ok(None) => Err("筛选配置不存在".to_string()),
            Err(e) => Err(format!("查询筛选配置失败: {}", e)),
        }
    }
}
